# 四驱巡线系统改为二驱系统 - 代码分析文档

## 项目概述

本项目原为基于MSPM0G3507微控制器的四轮驱动巡线小车系统，现已修改为二轮驱动系统。项目使用CCS开发环境，采用TI DriverLib库进行开发。

## 硬件平台
- **微控制器**: MSPM0G3507 (LQFP-64封装)
- **开发板**: LP-MSPM0G3507 LaunchPad
- **电机类型**: L型520电机 (MOTOR_TYPE = 5)
- **驱动方式**: 二轮差分驱动 (左右轮)

## 代码模块分析

### 1. empty.c - 主程序模块

**功能实现:**
- 系统初始化和主循环控制
- 电机类型配置 (修改为MOTOR_TYPE = 5)
- PID参数设置
- 巡线算法调用

**主要修改:**
```c
#define MOTOR_TYPE 5   // 改为L型520电机
```

**关键函数:**
- `main()`: 主函数，初始化系统并启动巡线
- `Set_Motor(MOTOR_TYPE)`: 配置电机参数
- `send_motor_PID(1.9,0.2,0.8)`: 设置PID参数
- `Four_LineWalking()`: 巡线主循环

### 2. app_motor.c - 电机应用层控制

**功能实现:**
- 电机参数配置管理
- 运动控制算法 (差分驱动)
- 速度限制和安全保护

**主要修改:**
1. **简化电机类型配置** - 仅保留MOTOR_TYPE==5:
```c
void Set_Motor(int MOTOR_TYPE)
{
    // 仅支持MOTOR_TYPE == 5 (L型520电机) - 二驱系统
    if(MOTOR_TYPE == 5)
    {
        send_motor_type(1);
        send_pulse_phase(40);      // 减速比: 40
        send_pulse_line(11);       // 磁环线数: 11
        send_wheel_diameter(67.00); // 轮径: 67mm
        send_motor_deadzone(1900);  // 死区: 1900
    }
}
```

2. **二驱运动控制算法**:
```c
void Motion_Car_Control(int16_t V_x, int16_t V_y, int16_t V_z)
{
    // 差分驱动计算
    speed_L_setup = speed_fb + speed_spin;  // 左轮 = 前进 + 转向
    speed_R_setup = speed_fb - speed_spin;  // 右轮 = 前进 - 转向
    
    // 速度限制 [-1000, 1000]
    // 发送控制指令: 左轮(M1), 0(M2), 右轮(M3), 0(M4)
    Contrl_Speed(speed_L_setup, 0, speed_R_setup, 0);
}
```

**关键参数:**
- `Car_APB = 188.0f`: 小车轴距参数
- 速度范围: -1000 ~ +1000
- 电机配置: 减速比40, 磁环11线, 轮径67mm

### 3. app_motor_usart.h/.c - 电机串口通信

**功能实现:**
- 电机驱动板通信协议
- 参数配置指令发送
- 编码器数据接收和解析
- 速度和PWM控制指令

**通信协议格式:**
- 发送格式: `$command:data#`
- 接收格式: `$type:data1,data2,data3,data4#`

**主要函数:**
1. **配置指令发送:**
```c
send_motor_type(motor_type_t data);     // 电机类型
send_motor_deadzone(uint16_t data);     // 死区设置  
send_pulse_phase(uint16_t data);        // 减速比
send_pulse_line(uint16_t data);         // 磁环线数
send_wheel_diameter(float data);        // 轮径
send_motor_PID(float P,float I,float D); // PID参数
```

2. **控制指令发送:**
```c
// 二驱系统: M1=左电机, M3=右电机, M2&M4=0
Contrl_Speed(int16_t M1_speed, int16_t M2_speed, int16_t M3_speed, int16_t M4_speed);
Contrl_Pwm(int16_t M1_pwm, int16_t M2_pwm, int16_t M3_pwm, int16_t M4_pwm);
```

3. **数据接收解析:**
```c
Deal_Control_Rxtemp(uint8_t rxtemp);    // 接收数据处理
Deal_data_real(void);                   // 数据解析
```

**数据结构:**
- `g_Speed[4]`: 四个电机速度反馈
- `Encoder_Now[4]`: 编码器当前值
- `Encoder_Offset[4]`: 编码器增量值

### 4. bsp_motor_usart.h/.c - 电机串口底层驱动

**功能实现:**
- UART1硬件接口封装
- 数据发送和接收中断处理
- 与电机驱动板的物理通信

**关键函数:**
```c
Send_Motor_U8(uint8_t Data);                    // 发送单字节
Send_Motor_ArrayU8(uint8_t *pData, uint16_t Length); // 发送数组
UART_1_INST_IRQHandler(void);                   // 串口中断处理
```

### 5. Four_linewalking.h/.c - 巡线算法模块

**功能实现:**
- 四路红外传感器读取
- 巡线状态判断和决策
- PID控制算法
- 运动控制输出

**传感器配置:**
```c
// 从左到右传感器顺序: L2 L1 R1 R2
#define LineWalk_L1_IN  // 左内侧传感器
#define LineWalk_L2_IN  // 左外侧传感器  
#define LineWalk_R1_IN  // 右内侧传感器
#define LineWalk_R2_IN  // 右外侧传感器
```

**巡线逻辑:**
1. **直线行驶**: L2=0, R1=0 → err=0
2. **左转**: L2=0, R1=1 → err=-1  
3. **右转**: L2=1, R1=0 → err=1
4. **急转弯**: 检测到外侧传感器 → err=±9
5. **直角转弯**: 多传感器触发 → err=±13

**PID控制:**
```c
float APP_IR_PID_Calc(float actual_value)
{
    // 位置式PID: Kp=450, Ki=0, Kd=0
    IRTrackTurn = error*IRTrack_Trun_KP + IRTrack_Trun_KI*IRTrack_Integral + (error - error_last)*IRTrack_Trun_KD;
    return IRTrackTurn;
}
```

**运动控制:**
```c
Motion_Car_Control(IRR_SPEED, 0, pid_output_IRR);
// IRR_SPEED=300: 基础前进速度
// pid_output_IRR: PID输出的转向控制量
```

### 6. delay.h/.c - 精确延时模块

**功能实现:**
- 基于SysTick的精确微秒延时
- 毫秒延时封装

**关键函数:**
```c
void delay_us(unsigned long __us);  // 微秒延时
void delay_ms(unsigned long ms);    // 毫秒延时  
```

### 7. usart.h/.c - 调试串口模块

**功能实现:**
- UART0调试串口初始化
- printf重定向支持
- 串口数据收发

**关键函数:**
```c
void USART_Init(void);              // 串口初始化
void USART_SendData(unsigned char data); // 发送数据
int fputc(int ch, FILE *stream);    // printf重定向
UART_0_INST_IRQHandler(void);       // 串口中断
```

## 二驱系统修改总结

### 主要修改点:
1. **电机类型**: 改为MOTOR_TYPE = 5 (L型520电机)
2. **运动控制**: 四轮改为差分驱动 (左右轮)
3. **控制映射**: M1=左电机, M3=右电机, M2&M4=0
4. **代码简化**: 删除其他电机类型配置代码

### 保留功能:
1. **巡线算法**: 完全保留四路传感器逻辑
2. **通信协议**: 保持与驱动板兼容
3. **PID控制**: 保留原有控制参数
4. **调试功能**: 保留串口调试支持

### 系统优势:
1. **结构简化**: 减少机械复杂度
2. **成本降低**: 减少电机和驱动器数量  
3. **控制精确**: 差分驱动转向更精确
4. **维护简单**: 减少故障点

## 使用说明

1. **硬件连接**: 确保左右电机正确连接到M1和M3端口
2. **参数调试**: 根据实际小车调整PID参数和轴距
3. **传感器校准**: 确保四路红外传感器正常工作
4. **速度调节**: 可调整IRR_SPEED基础速度

## 技术细节

### 电机控制映射关系
```
原四驱系统:     修改后二驱系统:
M1 - 左前轮  →  M1 - 左轮 (主要)
M2 - 左后轮  →  M2 - 0 (禁用)
M3 - 右前轮  →  M3 - 右轮 (主要)
M4 - 右后轮  →  M4 - 0 (禁用)
```

### 差分驱动算法
```c
// 输入参数:
// V_x: 前进速度 (-1000 ~ +1000)
// V_z: 转向速度 (-1000 ~ +1000)

// 计算公式:
speed_spin = (V_z / 1000.0f) * Car_APB;  // 转向分量
speed_L = V_x + speed_spin;              // 左轮速度
speed_R = V_x - speed_spin;              // 右轮速度

// 运动效果:
// V_z > 0: 右转 (左轮快，右轮慢)
// V_z < 0: 左转 (左轮慢，右轮快)
// V_z = 0: 直行 (左右轮同速)
```

### 巡线传感器状态表
| L2 | L1 | R1 | R2 | 状态描述 | 误差值 | 动作 |
|----|----|----|----|---------|----|------|
| 0  | 0  | 0  | 0  | 全黑线   | 0   | 直行 |
| X  | 0  | 0  | X  | 中央黑线 | 0   | 直行 |
| X  | 0  | 1  | X  | 左偏     | -1  | 微调左转 |
| X  | 1  | 0  | X  | 右偏     | 1   | 微调右转 |
| 0  | X  | X  | X  | 最左检测 | -9  | 左转 |
| X  | X  | X  | 0  | 最右检测 | 9   | 右转 |
| 0/1| 0  | X  | 0  | 右急转   | 13  | 急右转 |
| 0  | X  | 0  | 0/1| 左急转   | -13 | 急左转 |

### 通信协议详解

**发送指令格式:**
```
$mtype:1#           - 设置电机类型
$mphase:40#         - 设置减速比
$mline:11#          - 设置磁环线数
$wdiameter:67.000#  - 设置轮径
$deadzone:1900#     - 设置死区
$mpid:1.900,0.200,0.800# - 设置PID参数
$spd:300,0,-300,0#  - 速度控制 (左转示例)
$pwm:500,0,-500,0#  - PWM控制 (左转示例)
```

**接收数据格式:**
```
$MAll:1234,0,5678,0#     - 编码器总值
$MTEP:12,0,34,0#         - 编码器增量 (10ms)
$MSPD:123.5,0.0,234.6,0.0# - 速度反馈
```

### 系统时序

**初始化序列:**
1. SYSCFG_DL_init() - 系统初始化
2. USART_Init() - 串口初始化
3. Set_Motor(5) - 电机参数配置
4. send_motor_PID() - PID参数设置
5. delay_ms(100) - 等待稳定
6. Four_LineWalking() - 开始巡线

**巡线控制周期:**
1. 读取传感器状态 (4路)
2. 计算误差值 (-13 ~ +13)
3. PID控制计算
4. 运动控制输出
5. 延时处理 (10-80ms)

## 性能参数

### 电机参数 (MOTOR_TYPE = 5)
- **电机类型**: L型520减速电机
- **减速比**: 1:40
- **编码器**: 11线磁环
- **轮径**: 67mm
- **死区**: 1900 (PWM值)
- **最大速度**: ±1000 (控制量)

### 控制参数
- **基础速度**: 300 (IRR_SPEED)
- **PID参数**: Kp=450, Ki=0, Kd=0
- **轴距**: 188mm (Car_APB)
- **控制频率**: 约100Hz (取决于传感器处理时间)

### 通信参数
- **调试串口**: UART0, 115200bps
- **电机串口**: UART1, 115200bps
- **协议格式**: ASCII文本协议
- **数据更新**: 10ms周期 (编码器)

## 故障排除

### 常见问题
1. **小车不动**: 检查电机连接和死区设置
2. **转向异常**: 检查轴距参数和PID设置
3. **巡线偏移**: 校准传感器和调整阈值
4. **通信异常**: 检查串口连接和波特率

### 调试方法
1. **串口监控**: 使用printf输出调试信息
2. **传感器测试**: 打印传感器状态
3. **速度监控**: 输出左右轮速度值
4. **参数调节**: 逐步调整PID和速度参数

## 扩展建议

### 功能扩展
1. **速度闭环**: 利用编码器反馈实现精确速度控制
2. **路径规划**: 添加更复杂的路径跟踪算法
3. **障碍检测**: 集成超声波或红外测距传感器
4. **无线控制**: 添加WiFi或蓝牙通信模块

### 硬件升级
1. **更高精度编码器**: 提升位置控制精度
2. **IMU传感器**: 增加姿态反馈
3. **摄像头模块**: 实现视觉巡线
4. **更强处理器**: 支持更复杂算法

## 注意事项

1. 保持与原驱动板的通信协议兼容
2. M2和M4端口设为0，但仍发送完整4路指令
3. 编码器数据仍按4路接收，便于后续扩展
4. PID参数可能需要根据二驱特性重新调试
5. 确保左右电机方向一致，必要时调整接线
6. 定期校准传感器，确保巡线精度
