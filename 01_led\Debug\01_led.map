******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Tue Dec  3 19:20:55 2024

OUTPUT FILE NAME:   <01_led.out>
ENTRY POINT SYMBOL: "_c_int00_noinit_noargs"  address: 000003f9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000460  0001fba0  R  X
  SRAM                  20200000   00008000  00000200  00007e00  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000460   00000460    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000003a0   000003a0    r-x .text
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000003a0     
                  000000c0    000002d0     empty.o (.text.main)
                  00000390    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000003cc    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000003f8    00000020     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noinit_noargs)
                  00000418    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000434    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000444    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000044e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000452    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000456    00000004            : exit.c.obj (.text:abort)
                  0000045a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000045c    00000004     --HOLE-- [fill = 0]

.cinit     0    00000000    00000000     

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       empty.o                        720    0         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       ti_msp_dl_config.o             148    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         874    192       0      
                                                              
    D:/ti/mspm0_sdk_2_03_00_07/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         10     0         0      
                                                              
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       boot_cortex_m.c.obj            32     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         40     0         0      
                                                              
       Stack:                         0      0         512    
    +--+------------------------------+------+---------+---------+
       Grand Total:                   924    192       512    


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                    
-------   ----                    
0000045b  ADC0_IRQHandler         
0000045b  ADC1_IRQHandler         
0000045b  AES_IRQHandler          
00000456  C$$EXIT                 
0000045b  CANFD0_IRQHandler       
0000045b  DAC0_IRQHandler         
00000445  DL_Common_delayCycles   
0000045b  DMA_IRQHandler          
0000045b  Default_Handler         
0000045b  GROUP0_IRQHandler       
0000045b  GROUP1_IRQHandler       
0000045b  HardFault_Handler       
0000045b  I2C0_IRQHandler         
0000045b  I2C1_IRQHandler         
0000045b  NMI_Handler             
0000045b  PendSV_Handler          
0000045b  RTC_IRQHandler          
0000044f  Reset_Handler           
0000045b  SPI0_IRQHandler         
0000045b  SPI1_IRQHandler         
0000045b  SVC_Handler             
00000419  SYSCFG_DL_GPIO_init     
00000391  SYSCFG_DL_SYSCTL_init   
00000435  SYSCFG_DL_init          
000003cd  SYSCFG_DL_initPower     
0000045b  SysTick_Handler         
0000045b  TIMA0_IRQHandler        
0000045b  TIMA1_IRQHandler        
0000045b  TIMG0_IRQHandler        
0000045b  TIMG12_IRQHandler       
0000045b  TIMG6_IRQHandler        
0000045b  TIMG7_IRQHandler        
0000045b  TIMG8_IRQHandler        
0000045b  UART0_IRQHandler        
0000045b  UART1_IRQHandler        
0000045b  UART2_IRQHandler        
0000045b  UART3_IRQHandler        
20208000  __STACK_END             
00000200  __STACK_SIZE            
00000000  __TI_ATRegion0_region_sz
00000000  __TI_ATRegion0_src_addr 
00000000  __TI_ATRegion0_trg_addr 
00000000  __TI_ATRegion1_region_sz
00000000  __TI_ATRegion1_src_addr 
00000000  __TI_ATRegion1_trg_addr 
00000000  __TI_ATRegion2_region_sz
00000000  __TI_ATRegion2_src_addr 
00000000  __TI_ATRegion2_trg_addr 
00000000  __TI_CINIT_Base         
00000000  __TI_CINIT_Limit        
00000000  __TI_CINIT_Warm         
ffffffff  __TI_pprof_out_hndl     
ffffffff  __TI_prof_data_size     
ffffffff  __TI_prof_data_start    
00000000  __TI_static_base__      
ffffffff  __binit__               
UNDEFED   __mpu_init              
20207e00  __stack                 
20200000  __start___llvm_prf_bits 
20200000  __start___llvm_prf_cnts 
20200000  __stop___llvm_prf_bits  
20200000  __stop___llvm_prf_cnts  
000003f9  _c_int00_noinit_noargs  
00000453  _system_pre_init        
00000457  abort                   
ffffffff  binit                   
00000000  interruptVectors        
000000c1  main                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                    
-------   ----                    
00000000  __TI_ATRegion0_region_sz
00000000  __TI_ATRegion0_src_addr 
00000000  __TI_ATRegion0_trg_addr 
00000000  __TI_ATRegion1_region_sz
00000000  __TI_ATRegion1_src_addr 
00000000  __TI_ATRegion1_trg_addr 
00000000  __TI_ATRegion2_region_sz
00000000  __TI_ATRegion2_src_addr 
00000000  __TI_ATRegion2_trg_addr 
00000000  __TI_CINIT_Base         
00000000  __TI_CINIT_Limit        
00000000  __TI_CINIT_Warm         
00000000  __TI_static_base__      
00000000  interruptVectors        
000000c1  main                    
00000200  __STACK_SIZE            
00000391  SYSCFG_DL_SYSCTL_init   
000003cd  SYSCFG_DL_initPower     
000003f9  _c_int00_noinit_noargs  
00000419  SYSCFG_DL_GPIO_init     
00000435  SYSCFG_DL_init          
00000445  DL_Common_delayCycles   
0000044f  Reset_Handler           
00000453  _system_pre_init        
00000456  C$$EXIT                 
00000457  abort                   
0000045b  ADC0_IRQHandler         
0000045b  ADC1_IRQHandler         
0000045b  AES_IRQHandler          
0000045b  CANFD0_IRQHandler       
0000045b  DAC0_IRQHandler         
0000045b  DMA_IRQHandler          
0000045b  Default_Handler         
0000045b  GROUP0_IRQHandler       
0000045b  GROUP1_IRQHandler       
0000045b  HardFault_Handler       
0000045b  I2C0_IRQHandler         
0000045b  I2C1_IRQHandler         
0000045b  NMI_Handler             
0000045b  PendSV_Handler          
0000045b  RTC_IRQHandler          
0000045b  SPI0_IRQHandler         
0000045b  SPI1_IRQHandler         
0000045b  SVC_Handler             
0000045b  SysTick_Handler         
0000045b  TIMA0_IRQHandler        
0000045b  TIMA1_IRQHandler        
0000045b  TIMG0_IRQHandler        
0000045b  TIMG12_IRQHandler       
0000045b  TIMG6_IRQHandler        
0000045b  TIMG7_IRQHandler        
0000045b  TIMG8_IRQHandler        
0000045b  UART0_IRQHandler        
0000045b  UART1_IRQHandler        
0000045b  UART2_IRQHandler        
0000045b  UART3_IRQHandler        
20200000  __start___llvm_prf_bits 
20200000  __start___llvm_prf_cnts 
20200000  __stop___llvm_prf_bits  
20200000  __stop___llvm_prf_cnts  
20207e00  __stack                 
20208000  __STACK_END             
ffffffff  __TI_pprof_out_hndl     
ffffffff  __TI_prof_data_size     
ffffffff  __TI_prof_data_start    
ffffffff  __binit__               
ffffffff  binit                   
UNDEFED   __mpu_init              

[68 symbols]
