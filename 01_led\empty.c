#include "ti_msp_dl_config.h"

//自定义延时（不精确） Custom delay (not precise)
void delay_ms(unsigned int ms)
{
    unsigned int i, j;
    // 下面的嵌套循环的次数是根据主控频率和编译器生成的指令周期大致计算出来的，需要通过实际测试调整来达到所需的延时。
		// The number of nested loops below is roughly calculated based on the master control frequency and the instruction cycle generated by the compiler, 
		// and needs to be adjusted through actual testing to achieve the required delay.
    for (i = 0; i < ms; i++)
    {
        for (j = 0; j < 8000; j++)
        {
            // 仅执行一个足够简单以致于可以预测其执行时间的操作
						// Perform only one operation that is simple enough to predict its execution time
            __asm__("nop"); // "nop" 代表“无操作”，在大多数架构中，这会消耗一个或几个时钟周期  "nop" stands for "no operation", which on most architectures consumes one or a few clock cycles
        }
    }
}

int main(void)
{
    SYSCFG_DL_init();
    while (1)
    { 
        DL_GPIO_clearPins(LED1_PORT,LED1_PIN_2_PIN);//输出低电平 Output low level
//			  DL_GPIO_clearPins(LED2_PORT,LED2_PIN_3_PIN);//输出低电平 Output low level
        delay_ms(1000);//延时大概1S Delay about 1S
        DL_GPIO_setPins(LED1_PORT,LED1_PIN_2_PIN);  //输出高电平 Output high level
//			  DL_GPIO_setPins(LED2_PORT,LED2_PIN_3_PIN);  //输出高电平 Output high level
        delay_ms(1000);//延时大概1S Delay about 1S

    }
}