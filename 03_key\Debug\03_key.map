******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Nov 27 18:26:00 2024

OUTPUT FILE NAME:   <03_key.out>
ENTRY POINT SYMBOL: "_c_int00_noinit_noargs"  address: 000001b1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000001f8  0001fe08  R  X
  SRAM                  20200000   00008000  00000200  00007e00  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000001f8   000001f8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000138   00000138    r-x .text
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000138     
                  000000c0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000108    00000040     empty.o (.text.main)
                  00000148    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000184    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000001b0    00000020     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noinit_noargs)
                  000001d0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000001e0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000001ea    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000001ee    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000001f2    00000004            : exit.c.obj (.text:abort)
                  000001f6    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)

.cinit     0    00000000    00000000     

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       startup_mspm0g350x_ticlang.o   6      192       0      
       ti_msp_dl_config.o             192    0         0      
       empty.o                        64     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         262    192       0      
                                                              
    D:/ti/mspm0_sdk_2_02_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         10     0         0      
                                                              
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       boot_cortex_m.c.obj            32     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         40     0         0      
                                                              
       Stack:                         0      0         512    
    +--+------------------------------+------+---------+---------+
       Grand Total:                   312    192       512    


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                    
-------   ----                    
000001f7  ADC0_IRQHandler         
000001f7  ADC1_IRQHandler         
000001f7  AES_IRQHandler          
000001f2  C$$EXIT                 
000001f7  CANFD0_IRQHandler       
000001f7  DAC0_IRQHandler         
000001e1  DL_Common_delayCycles   
000001f7  DMA_IRQHandler          
000001f7  Default_Handler         
000001f7  GROUP0_IRQHandler       
000001f7  GROUP1_IRQHandler       
000001f7  HardFault_Handler       
000001f7  I2C0_IRQHandler         
000001f7  I2C1_IRQHandler         
000001f7  NMI_Handler             
000001f7  PendSV_Handler          
000001f7  RTC_IRQHandler          
000001eb  Reset_Handler           
000001f7  SPI0_IRQHandler         
000001f7  SPI1_IRQHandler         
000001f7  SVC_Handler             
000000c1  SYSCFG_DL_GPIO_init     
00000149  SYSCFG_DL_SYSCTL_init   
000001d1  SYSCFG_DL_init          
00000185  SYSCFG_DL_initPower     
000001f7  SysTick_Handler         
000001f7  TIMA0_IRQHandler        
000001f7  TIMA1_IRQHandler        
000001f7  TIMG0_IRQHandler        
000001f7  TIMG12_IRQHandler       
000001f7  TIMG6_IRQHandler        
000001f7  TIMG7_IRQHandler        
000001f7  TIMG8_IRQHandler        
000001f7  UART0_IRQHandler        
000001f7  UART1_IRQHandler        
000001f7  UART2_IRQHandler        
000001f7  UART3_IRQHandler        
20208000  __STACK_END             
00000200  __STACK_SIZE            
00000000  __TI_ATRegion0_region_sz
00000000  __TI_ATRegion0_src_addr 
00000000  __TI_ATRegion0_trg_addr 
00000000  __TI_ATRegion1_region_sz
00000000  __TI_ATRegion1_src_addr 
00000000  __TI_ATRegion1_trg_addr 
00000000  __TI_ATRegion2_region_sz
00000000  __TI_ATRegion2_src_addr 
00000000  __TI_ATRegion2_trg_addr 
00000000  __TI_CINIT_Base         
00000000  __TI_CINIT_Limit        
00000000  __TI_CINIT_Warm         
ffffffff  __TI_pprof_out_hndl     
ffffffff  __TI_prof_data_size     
ffffffff  __TI_prof_data_start    
00000000  __TI_static_base__      
ffffffff  __binit__               
UNDEFED   __mpu_init              
20207e00  __stack                 
20200000  __start___llvm_prf_bits 
20200000  __start___llvm_prf_cnts 
20200000  __stop___llvm_prf_bits  
20200000  __stop___llvm_prf_cnts  
000001b1  _c_int00_noinit_noargs  
000001ef  _system_pre_init        
000001f3  abort                   
ffffffff  binit                   
00000000  interruptVectors        
00000109  main                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                    
-------   ----                    
00000000  __TI_ATRegion0_region_sz
00000000  __TI_ATRegion0_src_addr 
00000000  __TI_ATRegion0_trg_addr 
00000000  __TI_ATRegion1_region_sz
00000000  __TI_ATRegion1_src_addr 
00000000  __TI_ATRegion1_trg_addr 
00000000  __TI_ATRegion2_region_sz
00000000  __TI_ATRegion2_src_addr 
00000000  __TI_ATRegion2_trg_addr 
00000000  __TI_CINIT_Base         
00000000  __TI_CINIT_Limit        
00000000  __TI_CINIT_Warm         
00000000  __TI_static_base__      
00000000  interruptVectors        
000000c1  SYSCFG_DL_GPIO_init     
00000109  main                    
00000149  SYSCFG_DL_SYSCTL_init   
00000185  SYSCFG_DL_initPower     
000001b1  _c_int00_noinit_noargs  
000001d1  SYSCFG_DL_init          
000001e1  DL_Common_delayCycles   
000001eb  Reset_Handler           
000001ef  _system_pre_init        
000001f2  C$$EXIT                 
000001f3  abort                   
000001f7  ADC0_IRQHandler         
000001f7  ADC1_IRQHandler         
000001f7  AES_IRQHandler          
000001f7  CANFD0_IRQHandler       
000001f7  DAC0_IRQHandler         
000001f7  DMA_IRQHandler          
000001f7  Default_Handler         
000001f7  GROUP0_IRQHandler       
000001f7  GROUP1_IRQHandler       
000001f7  HardFault_Handler       
000001f7  I2C0_IRQHandler         
000001f7  I2C1_IRQHandler         
000001f7  NMI_Handler             
000001f7  PendSV_Handler          
000001f7  RTC_IRQHandler          
000001f7  SPI0_IRQHandler         
000001f7  SPI1_IRQHandler         
000001f7  SVC_Handler             
000001f7  SysTick_Handler         
000001f7  TIMA0_IRQHandler        
000001f7  TIMA1_IRQHandler        
000001f7  TIMG0_IRQHandler        
000001f7  TIMG12_IRQHandler       
000001f7  TIMG6_IRQHandler        
000001f7  TIMG7_IRQHandler        
000001f7  TIMG8_IRQHandler        
000001f7  UART0_IRQHandler        
000001f7  UART1_IRQHandler        
000001f7  UART2_IRQHandler        
000001f7  UART3_IRQHandler        
00000200  __STACK_SIZE            
20200000  __start___llvm_prf_bits 
20200000  __start___llvm_prf_cnts 
20200000  __stop___llvm_prf_bits  
20200000  __stop___llvm_prf_cnts  
20207e00  __stack                 
20208000  __STACK_END             
ffffffff  __TI_pprof_out_hndl     
ffffffff  __TI_prof_data_size     
ffffffff  __TI_prof_data_start    
ffffffff  __binit__               
ffffffff  binit                   
UNDEFED   __mpu_init              

[68 symbols]
