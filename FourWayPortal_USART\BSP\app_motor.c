#include "app_motor.h"

static float speed_lr = 0;
static float speed_fb = 0;
static float speed_spin = 0;
static int speed_L_setup = 0;  // ������ Left motor
static int speed_R_setup = 0;  // ������ Right motor

// ���ص�ǰС����������͵�һ��
static float Motion_Get_APB(void)
{
    return Car_APB;
}

void Set_Motor(int MOTOR_TYPE)
{
    // ֻ֧��MOTOR_TYPE == 5 (L��520����) - ������ϵͳ
    // Only support MOTOR_TYPE == 5 (L type 520 motor) - Two-wheel drive system
    if(MOTOR_TYPE == 5)
    {
        send_motor_type(1);
        delay_ms(100);
        send_pulse_phase(40);
        delay_ms(100);
        send_pulse_line(11);
        delay_ms(100);
        send_wheel_diameter(67.00);
        delay_ms(100);
        send_motor_deadzone(1900);
        delay_ms(100);
    }
}

//ֱ�ӿ���pwm - ������ϵͳ Direct PWM control - Two-wheel drive system
void Motion_Car_Control(int16_t V_x, int16_t V_y, int16_t V_z)
{
	float robot_APB = Motion_Get_APB();
	speed_lr = 0;
    speed_fb = V_x;
    speed_spin = (V_z / 1000.0f) * robot_APB;
    if (V_x == 0 && V_y == 0 && V_z == 0)
    {
        // ������ϵͳ��ֻ��Ҫ���������� Two-wheel drive system only needs left and right motors
        Contrl_Speed(0, 0, 0, 0);  // ����ǰ������ Keep forward compatibility
        return;
    }

    // ������ϵͳ���㣺������ = ǰ���ٶ� + ��ת���� Two-wheel drive calculation: left motor = forward speed + rotation component
    speed_L_setup = speed_fb + speed_spin;
    speed_R_setup = speed_fb - speed_spin;

    // �����ٶ����� Speed limiting
    if (speed_L_setup > 1000) speed_L_setup = 1000;
    if (speed_L_setup < -1000) speed_L_setup = -1000;
    if (speed_R_setup > 1000) speed_R_setup = 1000;
    if (speed_R_setup < -1000) speed_R_setup = -1000;

    //printf("Left:%d\t,Right:%d\r\n", speed_L_setup, speed_R_setup);

    // ������ϵͳ��������������������Ϊ0 Two-wheel drive system: left and right motors, rear motors set to 0
    Contrl_Speed(speed_L_setup, 0, speed_R_setup, 0);

}

