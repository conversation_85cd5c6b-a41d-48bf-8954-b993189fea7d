/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

GPIO1.$name                               = "KEY";
GPIO1.port                                = "PORTA";
GPIO1.associatedPins[0].$name             = "PIN_18";
GPIO1.associatedPins[0].direction         = "INPUT";
GPIO1.associatedPins[0].internalResistor  = "PULL_DOWN";
GPIO1.associatedPins[0].hysteresisControl = "DISABLE";
GPIO1.associatedPins[0].assignedPin       = "18";
GPIO1.associatedPins[0].interruptEn       = true;
GPIO1.associatedPins[0].polarity          = "RISE";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "LED1";
GPIO2.port                               = "PORTB";
GPIO2.associatedPins[0].$name            = "PIN_2";
GPIO2.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[0].assignedPin      = "2";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PA18";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB2";
